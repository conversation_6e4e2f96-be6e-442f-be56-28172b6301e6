/**
 * WebSocket Server for OMNI-ALPHA VΩ∞∞ Trading System Dashboard
 *
 * This module sets up the WebSocket server for real-time updates to the dashboard.
 * It provides real-time data streams for various aspects of the trading system.
 */

const tradingStrategyService = require('../services/trading-strategy-service');
const dataCache = require('../utils/data-cache.js');
const agentOrchestrator = require('../services/agent-orchestrator');
const quantumBridge = require('../services/quantum-bridge');
const multiAgentCoordinator = require('../services/multi-agent-coordinator');
const bybitClient = require('../utils/bybit-client');
const logger = require('../utils/logger');

// Active connections
const connections = new Set();

// Setup WebSocket server
function setupWebSocketServer(io) {
  logger.info('Setting up WebSocket server');

  io.on('connection', (socket) => {
    logger.info(`Client connected: ${socket.id}`);
    connections.add(socket);

    // Send initial data
    sendInitialData(socket);

    // Handle subscription to specific data streams
    socket.on('subscribe', (channel) => {
      logger.info(`Client ${socket.id} subscribed to ${channel}`);
      socket.join(channel);
    });

    // Handle unsubscription from specific data streams
    socket.on('unsubscribe', (channel) => {
      logger.info(`Client ${socket.id} unsubscribed from ${channel}`);
      socket.leave(channel);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Client disconnected: ${socket.id}`);
      connections.delete(socket);
    });
  });

  // Start sending periodic updates
  startPeriodicUpdates(io);

  return io;
}

module.exports = { setupWebSocketServer };
