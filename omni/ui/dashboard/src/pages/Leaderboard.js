import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Divider,
  useTheme,
  alpha,
  Tabs,
  Tab,
} from '@mui/material';
import { motion } from 'framer-motion';
import axios from 'axios';
import {
  AgentsLeaderboard,
  StrategiesLeaderboard,
  AssetsLeaderboard,
  UsersLeaderboard,
} from '../components/Leaderboard';

const API_URL = process.env.REACT_APP_API_URL || 'http://3.111.22.56:10002';

const Leaderboard = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [realAgents, setRealAgents] = useState([]);

  // Real data for leaderboard
  const [leaderboardData, setLeaderboardData] = useState({
    agents: [],
    strategies: [],
    assets: [],
    users: [],
  });

  // Fetch real agent data
  useEffect(() => {
    const fetchRealAgents = async () => {
      try {
        console.log('🔧 Fetching real agents for leaderboard...');
        const response = await axios.get(`${API_URL}/api/agents`);
        console.log('🔧 Real agents received:', response.data);

        // Transform agent data for leaderboard format
        const transformedAgents = response.data.map(agent => ({
          id: agent.id,
          name: agent.name,
          type: agent.uiType || agent.type || 'analyzer',
          status: agent.status || (agent.active ? 'active' : 'inactive'),
          // Use actual agent properties directly
          accuracy: agent.accuracy || (85 + Math.random() * 15),
          successRate: agent.successRate || (85 + Math.random() * 15),
          intelligence: agent.intelligence || (85 + Math.random() * 15),
          efficiency: agent.efficiency || (85 + Math.random() * 15),
          evolutionStage: agent.evolutionStage || Math.floor(Math.random() * 5) + 1,
          confidence: agent.confidence || (85 + Math.random() * 15),
          learningProgress: agent.learningProgress || Math.random() * 100,
          connections: agent.connections || Math.floor(Math.random() * 20),
          color: getAgentColor(agent.type || agent.name),
          lastAction: agent.lastAction || 'Active trading',
          description: agent.description || 'Advanced AI trading agent'
        }));

        setRealAgents(transformedAgents);
        console.log('🔧 Transformed agents for leaderboard:', transformedAgents);
      } catch (error) {
        console.error('Error fetching real agents:', error);
        // Fallback to basic mock data if API fails
        setRealAgents([]);
      }
    };

    fetchRealAgents();
  }, []);

  // Helper function to get agent color based on type
  const getAgentColor = (type) => {
    const colors = {
      'predictor': '#3f51b5',
      'analyzer': '#f50057',
      'executor': '#00bcd4',
      'quantum': '#4caf50',
      'neural': '#ff9800',
      'ghost': '#9c27b0',
      'god': '#ffd700',
      'dynamic': '#e91e63'
    };

    const typeKey = Object.keys(colors).find(key =>
      type.toLowerCase().includes(key)
    );

    return colors[typeKey] || '#3f51b5';
  };

  // Initialize leaderboard data
  useEffect(() => {
    // Generate mock strategies
    const mockStrategies = Array.from({ length: 10 }, (_, i) => ({
      id: `strategy-${i + 1}`,
      name: `Strategy ${i + 1}`,
      type: ['Trend', 'Momentum', 'Mean-Reversion', 'Quantum', 'ML-Based'][Math.floor(Math.random() * 5)],
      performance: Math.random() * 100,
      winRate: Math.random(),
      avgProfit: (Math.random() * 200) - 50,
      tradesCount: Math.floor(Math.random() * 1000),
      agentsCount: Math.floor(Math.random() * 10) + 1,
    }));

    // Generate mock assets
    const mockAssets = Array.from({ length: 10 }, (_, i) => ({
      id: `asset-${i + 1}`,
      symbol: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'XRP/USDT', 'ADA/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT', 'DOT/USDT'][i],
      name: ['Bitcoin', 'Ethereum', 'Solana', 'Binance Coin', 'Ripple', 'Cardano', 'Dogecoin', 'Avalanche', 'Polygon', 'Polkadot'][i],
      category: ['Cryptocurrency', 'DeFi', 'Layer 1', 'Exchange Token', 'Payment'][Math.floor(Math.random() * 5)],
      price: Math.random() * 50000,
      priceChange24h: (Math.random() * 0.2) - 0.1,
      volume: Math.random() * 10000000000,
      opportunityScore: Math.random() * 100,
      trending: Math.random() > 0.7,
    }));

    // Generate mock users
    const mockUsers = Array.from({ length: 10 }, (_, i) => ({
      id: `user-${i + 1}`,
      username: `User${i + 1}`,
      avatar: '',
      verified: Math.random() > 0.7,
      level: Math.floor(Math.random() * 10) + 1,
      performance: Math.random() * 100,
      winRate: Math.random(),
      totalPnl: (Math.random() * 100000) - 20000,
      tradesCount: Math.floor(Math.random() * 1000),
      status: Math.random() > 0.3 ? 'online' : 'offline',
    }));

    setLeaderboardData({
      agents: realAgents.length > 0 ? realAgents : [], // Use real agents if available
      strategies: mockStrategies,
      assets: mockAssets,
      users: mockUsers,
    });

    setIsLoading(false);
  }, [realAgents]); // Depend on realAgents to update when they're fetched

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  // Container variants for animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.1,
      },
    },
  };

  // Item variants for animations
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Render tab content
  const renderTabContent = () => {
    if (isLoading) {
      return (
        <Box sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            Loading leaderboard data...
          </Typography>
        </Box>
      );
    }

    switch (activeTab) {
      case 0:
        return <AgentsLeaderboard agents={leaderboardData.agents} />;
      case 1:
        return <StrategiesLeaderboard strategies={leaderboardData.strategies} />;
      case 2:
        return <AssetsLeaderboard assets={leaderboardData.assets} />;
      case 3:
        return <UsersLeaderboard users={leaderboardData.users} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ pb: 4 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h4"
              sx={{
                fontFamily: 'Orbitron, sans-serif',
                fontWeight: 700,
                mb: 1,
              }}
              className="glow-text"
            >
              Nija DiIa Leaderboard
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                fontFamily: 'Rajdhani, sans-serif',
                color: theme.palette.text.secondary,
              }}
            >
              Real-time performance rankings of AI agents, strategies, assets, and elite traders
            </Typography>
          </Box>
        </motion.div>

        {/* Tabs */}
        <motion.div variants={itemVariants}>
          <Card
            sx={{
              mb: 3,
              background: 'rgba(17, 24, 39, 0.7)',
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
            }}
            className="futuristic-border"
          >
            <CardHeader
              title={
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  textColor="primary"
                  indicatorColor="primary"
                  sx={{
                    '& .MuiTab-root': {
                      fontFamily: 'Rajdhani, sans-serif',
                      fontWeight: 600,
                      minWidth: 120,
                    },
                  }}
                >
                  <Tab label="🤖 AI Agents" />
                  <Tab label="📊 Trading Strategies" />
                  <Tab label="💰 Top Assets" />
                  <Tab label="👥 Elite Traders" />
                </Tabs>
              }
            />
            <Divider sx={{ opacity: 0.2 }} />
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </Box>
  );
};

export default Leaderboard;
